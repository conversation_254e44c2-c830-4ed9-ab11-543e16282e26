"""
Cryptocurrency trading environment implementation.
"""

import random
from typing import Dict, <PERSON>, Tu<PERSON>, Optional, List
import numpy as np
import pandas as pd
from gymnasium import spaces
from collections import deque

from rl_ct.envs.base_env import BaseTradingEnv
from rl_ct.utils.data_loaders import DiskDataLoader
from rl_ct.utils.preprocessing import Scaler, QuantileScaler, SegmentedScaler
from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class CryptoTradingEnv(BaseTradingEnv):
    """Cryptocurrency trading environment with realistic features."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize crypto trading environment.

        Args:
            config: Environment configuration
        """
        super().__init__(config)

        # Trading specific parameters
        self.leverage = self.config.get('leverage', 1.0)
        self.order_size = self.config.get('order_size', 100.0)  # USD
        self.maintenance_margin_rate = self.config.get('maintenance_margin_rate', 0.01)

        # Data parameters
        self.dataset_name = self.config.get('dataset_name', 'default')
        self.lookback_window = self.config.get('lookback_window', 168)
        self.episode_length = self.config.get('episode_length', 168)

        # Training/evaluation splits
        self.train_start = self.config.get('train_start', [1000])
        self.train_end = self.config.get('train_end', [5000])
        self.test_start = self.config.get('test_start', [5000])
        self.test_end = self.config.get('test_end', [6000])
        self.regime = self.config.get('regime', 'training')

        # Position tracking
        self.position_long = 0.0  # Long position size
        self.position_short = 0.0  # Short position size
        self.avg_price_long = 0.0  # Average entry price for long
        self.avg_price_short = 0.0  # Average entry price for short
        self.unrealized_pnl = 0.0

        # Data storage
        self.price_data: Optional[np.ndarray] = None
        self.feature_data: Optional[np.ndarray] = None
        self.scaler: Optional[Scaler] = None
        self.obs_queue: Optional[deque] = None

        # Episode tracking
        self.episode_start_idx = 0
        self.data_idx = 0  # Current index in the data

        # Feature configuration - all features are treated uniformly
        # No separate time/technical feature handling

        # Load data and setup environment
        self._load_data()
        self._setup_spaces()

        logger.info(f"CryptoTradingEnv initialized with regime: {self.regime}")
        
    def _load_data(self) -> None:
        """Load market data and features."""
        logger.info(f"Loading dataset: {self.dataset_name}")
        
        # Load data using DiskDataLoader
        data_loader = DiskDataLoader(
            data_dir=self.config.get('data_dir', 'data/processed'),
            dataset_name=self.dataset_name,
            file_format=self.config.get('file_format', 'npy')
        )
        
        self.price_data, self.feature_data = data_loader.load_data()
        
        if self.price_data.size == 0 or self.feature_data.size == 0:
            raise ValueError(f"No data loaded for dataset: {self.dataset_name}")
        
        # Initialize scaler
        scaler_config = self.config.get('scaler', {})
        feature_dim = self.feature_data.shape[1] if self.feature_data is not None else 96

        # Use segmented scaler - all features from feature_data are treated as technical features
        # Account features are added separately
        self.scaler = SegmentedScaler(
            d_time=0,  # No separate time features
            d_account=2,  # available_balance, unrealized_pnl
            d_technical=feature_dim,  # All features from feature_data (including time features)
            time_scaler_type="none",  # No time features
            account_scaler_type="quantile",
            technical_scaler_type="quantile",
            min_quantile=scaler_config.get('min_quantile', 0.5),
            max_quantile=scaler_config.get('max_quantile', 99.5),
            scale_coef=scaler_config.get('scale_coef', self.initial_balance)
        )
        
        logger.info(f"Loaded data - Price: {self.price_data.shape}, Features: {self.feature_data.shape}")
        
    def _setup_spaces(self) -> None:
        """Setup action and observation spaces."""
        # Action space: 0=hold, 1=buy, 2=sell, 3=close_all
        self.action_space = spaces.Discrete(4)

        # Observation space: all features from feature_data + account features
        feature_dim = self.feature_data.shape[1] if self.feature_data is not None else 66
        account_dim = 2  # available_balance, unrealized_pnl
        obs_per_step = feature_dim + account_dim
        obs_dim = obs_per_step * self.lookback_window

        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(obs_dim,),
            dtype=np.float32
        )

        logger.debug(f"Action space: {self.action_space}")
        logger.debug(f"Observation space: {self.observation_space}")
        logger.debug(f"Observation breakdown: features={feature_dim}, account={account_dim}, total_per_step={obs_per_step}")
        
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """Reset environment for new episode."""
        super().reset(seed=seed)
        
        # Reset trading state
        self.position_long = 0.0
        self.position_short = 0.0
        self.avg_price_long = 0.0
        self.avg_price_short = 0.0
        self.unrealized_pnl = 0.0
        
        # Select episode start based on regime
        if self.regime == 'training':
            interval_idx = np.random.randint(len(self.train_start))
            start_range = self.train_start[interval_idx]
            end_range = self.train_end[interval_idx] - self.episode_length - 1
            self.episode_start_idx = np.random.randint(start_range, end_range)
        elif self.regime == 'evaluation':
            interval_idx = np.random.randint(len(self.test_start))
            start_range = self.test_start[interval_idx]
            end_range = self.test_end[interval_idx] - self.episode_length - 1
            self.episode_start_idx = np.random.randint(start_range, end_range)
        else:  # backtesting
            self.episode_start_idx = self.test_start[0]
            
        self.data_idx = self.episode_start_idx
        self.max_steps = self.episode_length
        
        # Initialize observation queue
        self.obs_queue = deque(maxlen=self.lookback_window)
        self.reset_queue = deque(maxlen=self.lookback_window * 4)
        
        state_array, reset_array = self._get_observation_reset()
        scaled_obs_window = self.scaler.reset(
            state_array,
            reset_array
        )

        # Flatten to create the final observation
        observation = scaled_obs_window.flatten()
        
        info = self._get_info()
        
        return observation, info

    def _get_observation_reset(self):
        for idx in range(self.episode_start_idx - self.lookback_window * 4, self.episode_start_idx):
            self._get_single_observation(idx)

        return np.array(self.obs_queue), np.array(self.reset_queue)
    
    def _get_single_observation(self, idx: int) -> np.ndarray:
        """Get observation for a single time step."""
        if idx < len(self.feature_data):
            all_features = self.feature_data[idx]

            # Extract time features from feature_data (hour, day_of_week)
            time_info = all_features[self.time_feature_indices]

            # Extract technical features (all features except time features)
            technical_mask = np.ones(len(all_features), dtype=bool)
            technical_mask[self.time_feature_indices] = False
            technical_features = all_features[technical_mask]
        else:
            # Fallback for out-of-bounds indices
            time_info = np.zeros(len(self.time_feature_indices))
            technical_features = np.zeros(self.feature_data.shape[1] - len(self.time_feature_indices))

        # Add account information
        available_balance = max(self.balance - self._calculate_margin(), 0)
        account_info = np.array([available_balance, self.unrealized_pnl])

        # Combine in the order expected by transformer: time, account, technical
        observation = np.hstack([time_info, account_info, technical_features]).astype(np.float32)
        self.obs_queue.append(observation)
        self.reset_queue.append(observation)

        return np.array(self.obs_queue)
        
    def _get_observation(self) -> np.ndarray:
        """Get current observation."""
        if self.obs_queue is None:
            return np.zeros(self.observation_space.shape, dtype=np.float32)

        # Get current observation and add to queue
        obs_step = self._get_single_observation(self.data_idx)
        # Scale the entire observation window
        scaled_obs = self.scaler.step(obs_step).flatten()

        return scaled_obs.astype(np.float32)
        
    def _execute_action(self, action: int) -> Dict[str, Any]:
        """Execute trading action."""
        current_price = self.get_current_price()
        execution_info = {
            'action': action,
            'price': current_price,
            'executed': False,
            'order_size': 0.0,
            'fee': 0.0
        }
        
        if action == 0:  # Hold
            pass
            
        elif action == 1:  # Buy (open/increase long position)
            if self._can_open_position(self.order_size):
                self._open_long_position(current_price, self.order_size)
                execution_info.update({
                    'executed': True,
                    'order_size': self.order_size,
                    'fee': self.order_size * self.transaction_cost
                })
                
        elif action == 2:  # Sell (open/increase short position or close long)
            if self.position_long > 0:
                # Close long position
                self._close_long_position(current_price)
                execution_info.update({
                    'executed': True,
                    'order_size': self.position_long,
                    'fee': self.position_long * current_price * self.transaction_cost
                })
            elif self._can_open_position(self.order_size):
                # Open short position
                self._open_short_position(current_price, self.order_size)
                execution_info.update({
                    'executed': True,
                    'order_size': self.order_size,
                    'fee': self.order_size * self.transaction_cost
                })
                
        elif action == 3:  # Close all positions
            if self.position_long > 0:
                self._close_long_position(current_price)
                execution_info['executed'] = True
            if self.position_short > 0:
                self._close_short_position(current_price)
                execution_info['executed'] = True
                
        # Update unrealized PnL
        self._update_unrealized_pnl(current_price)
        
        return execution_info

    def _open_long_position(self, price: float, size: float) -> None:
        """Open or increase long position."""
        # Calculate new average price
        total_value = self.position_long * self.avg_price_long + size
        total_size = self.position_long + size / price

        if total_size > 0:
            self.avg_price_long = total_value / total_size

        self.position_long = total_size

        # Deduct cost from balance
        cost = size + size * self.transaction_cost
        self.balance -= cost
        self.total_trades += 1

    def _open_short_position(self, price: float, size: float) -> None:
        """Open or increase short position."""
        # Calculate new average price
        total_value = self.position_short * self.avg_price_short + size
        total_size = self.position_short + size / price

        if total_size > 0:
            self.avg_price_short = total_value / total_size

        self.position_short = total_size

        # Deduct cost from balance
        cost = size + size * self.transaction_cost
        self.balance -= cost
        self.total_trades += 1

    def _close_long_position(self, price: float) -> None:
        """Close long position."""
        if self.position_long <= 0:
            return

        # Calculate PnL
        pnl = self.position_long * (price - self.avg_price_long)
        fee = self.position_long * price * self.transaction_cost
        net_pnl = pnl - fee

        # Update balance
        self.balance += self.position_long * price - fee

        # Track winning trades
        if net_pnl > 0:
            self.winning_trades += 1

        # Reset position
        self.position_long = 0.0
        self.avg_price_long = 0.0
        self.total_trades += 1

    def _close_short_position(self, price: float) -> None:
        """Close short position."""
        if self.position_short <= 0:
            return

        # Calculate PnL (profit when price goes down)
        pnl = self.position_short * (self.avg_price_short - price)
        fee = self.position_short * price * self.transaction_cost
        net_pnl = pnl - fee

        # Update balance
        self.balance += self.position_short * self.avg_price_short + net_pnl

        # Track winning trades
        if net_pnl > 0:
            self.winning_trades += 1

        # Reset position
        self.position_short = 0.0
        self.avg_price_short = 0.0
        self.total_trades += 1

    def _can_open_position(self, size: float) -> bool:
        """Check if we can open a position of given size."""
        required_margin = size / self.leverage
        available_balance = self.balance - self._calculate_margin()
        return available_balance >= required_margin + size * self.transaction_cost

    def _calculate_margin(self) -> float:
        """Calculate required margin for current positions."""
        margin_long = self.position_long * self.avg_price_long / self.leverage
        margin_short = self.position_short * self.avg_price_short / self.leverage
        return margin_long + margin_short

    def _update_unrealized_pnl(self, current_price: float) -> None:
        """Update unrealized PnL for current positions."""
        pnl_long = self.position_long * (current_price - self.avg_price_long) if self.position_long > 0 else 0
        pnl_short = self.position_short * (self.avg_price_short - current_price) if self.position_short > 0 else 0
        self.unrealized_pnl = pnl_long + pnl_short

    def _calculate_reward(self, action: int, prev_balance: float) -> float:
        """Calculate reward for the current step."""
        reward_type = self.config.get('reward_type', 'pnl')

        if reward_type == 'pnl':
            # Reward based on balance change
            balance_change = self.balance - prev_balance
            unrealized_change = self.unrealized_pnl
            total_change = balance_change + unrealized_change

            # Normalize by initial balance
            reward = total_change / self.initial_balance

        elif reward_type == 'sharpe':
            # Reward based on risk-adjusted returns
            total_value = self.balance + self.unrealized_pnl
            returns = (total_value - self.initial_balance) / self.initial_balance
            risk = max(self.max_drawdown, 0.01)
            reward = returns / risk

        elif reward_type == 'sortino':
            # Reward based on downside risk
            total_value = self.balance + self.unrealized_pnl
            returns = (total_value - self.initial_balance) / self.initial_balance
            downside_risk = max(self.max_drawdown, 0.01)
            reward = returns / downside_risk

        else:
            reward = 0.0

        # Apply reward scaling
        reward_scaling = self.config.get('reward_scaling', 1.0)
        return reward * reward_scaling

    def get_current_price(self) -> float:
        """Get current market price."""
        if self.price_data is None or self.data_idx >= len(self.price_data):
            return 0.0
        # Assume close price is in the last column or first column
        return float(self.price_data[self.data_idx, -1])

    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """Execute one step in the environment."""
        # Execute the step
        observation, reward, terminated, truncated, info = super().step(action)

        # Move to next data point
        self.data_idx += 1

        # Check for liquidation
        if self._is_liquidated():
            terminated = True
            reward -= 1.0  # Penalty for liquidation

        return observation, reward, terminated, truncated, info

    def _is_liquidated(self) -> bool:
        """Check if position should be liquidated."""
        if self.position_long == 0 and self.position_short == 0:
            return False

        current_price = self.get_current_price()

        # Calculate maintenance margin
        maintenance_margin = (
            self.position_long * current_price * self.maintenance_margin_rate +
            self.position_short * current_price * self.maintenance_margin_rate
        )

        # Check if equity is below maintenance margin
        equity = self.balance + self.unrealized_pnl
        return equity < maintenance_margin

    def _get_info(self) -> Dict[str, Any]:
        """Get environment info."""
        info = super()._get_info()
        info.update({
            'position_long': self.position_long,
            'position_short': self.position_short,
            'avg_price_long': self.avg_price_long,
            'avg_price_short': self.avg_price_short,
            'unrealized_pnl': self.unrealized_pnl,
            'current_price': self.get_current_price(),
            'data_idx': self.data_idx,
            'episode_start_idx': self.episode_start_idx,
            'margin_used': self._calculate_margin(),
            'leverage': self.leverage,
        })
        return info
