"""
Feature engineering utilities for creating technical indicators and features.
"""

from typing import Dict, List, Optional, Any
import pandas as pd
import ta

from rl_ct.utils.logger import get_logger

logger = get_logger(__name__)


class FeatureEngineer:
    """Feature engineering for cryptocurrency trading data."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize feature engineer.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.feature_names: List[str] = []
        
    def create_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create technical indicators from OHLCV data.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical indicators
        """
        logger.info("Creating technical indicators")
        
        # Make a copy to avoid modifying original data
        result_df = df.copy()
        
        # Price-based indicators
        result_df = self._add_price_indicators(result_df)
        
        # Volume-based indicators
        result_df = self._add_volume_indicators(result_df)
        
        # Momentum indicators
        result_df = self._add_momentum_indicators(result_df)
        
        # Volatility indicators
        result_df = self._add_volatility_indicators(result_df)
        
        # Trend indicators
        result_df = self._add_trend_indicators(result_df)
        
        # Support/Resistance indicators
        result_df = self._add_support_resistance_indicators(result_df)
        
        logger.info(f"Created {len(result_df.columns) - len(df.columns)} technical indicators")
        
        return result_df
        
    def _add_price_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price-based indicators."""
        # Simple Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = ta.trend.sma_indicator(df['close'], window=period)
            
        # Exponential Moving Averages
        for period in [12, 26, 50]:
            df[f'ema_{period}'] = ta.trend.ema_indicator(df['close'], window=period)
            
        # Price ratios
        df['close_sma_20_ratio'] = df['close'] / df['sma_20']
        df['sma_5_sma_20_ratio'] = df['sma_5'] / df['sma_20']
        
        # Price position within candle
        df['close_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['close_position'] = df['close_position'].fillna(0.5)
        
        return df
        
    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators."""
        # Volume Moving Average
        df['volume_sma_20'] = ta.volume.sma_ease_of_movement(
            df['high'], df['low'], df['volume'], window=20
        )
        
        # On-Balance Volume
        df['obv'] = ta.volume.on_balance_volume(df['close'], df['volume'])
        
        # Volume Price Trend
        df['vpt'] = ta.volume.volume_price_trend(df['close'], df['volume'])
        
        # Accumulation/Distribution Line
        df['ad'] = ta.volume.acc_dist_index(df['high'], df['low'], df['close'], df['volume'])
        
        # Chaikin Money Flow
        df['cmf'] = ta.volume.chaikin_money_flow(
            df['high'], df['low'], df['close'], df['volume'], window=20
        )
        
        return df
        
    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum indicators."""
        # RSI
        for period in [14, 21]:
            df[f'rsi_{period}'] = ta.momentum.rsi(df['close'], window=period)
            
        # Stochastic Oscillator
        df['stoch_k'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
        df['stoch_d'] = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
        
        # Williams %R
        df['williams_r'] = ta.momentum.williams_r(df['high'], df['low'], df['close'])
        
        # Rate of Change
        for period in [10, 20]:
            df[f'roc_{period}'] = ta.momentum.roc(df['close'], window=period)
            
        # MACD
        df['macd'] = ta.trend.macd(df['close'])
        df['macd_signal'] = ta.trend.macd_signal(df['close'])
        df['macd_diff'] = ta.trend.macd_diff(df['close'])
        
        return df
        
    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility indicators."""
        # Bollinger Bands
        df['bb_high'] = ta.volatility.bollinger_hband(df['close'])
        df['bb_low'] = ta.volatility.bollinger_lband(df['close'])
        df['bb_mid'] = ta.volatility.bollinger_mavg(df['close'])
        df['bb_width'] = (df['bb_high'] - df['bb_low']) / df['bb_mid']
        df['bb_position'] = (df['close'] - df['bb_low']) / (df['bb_high'] - df['bb_low'])
        
        # Average True Range
        df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])
        
        # Keltner Channels
        df['kc_high'] = ta.volatility.keltner_channel_hband(df['high'], df['low'], df['close'])
        df['kc_low'] = ta.volatility.keltner_channel_lband(df['high'], df['low'], df['close'])
        df['kc_mid'] = ta.volatility.keltner_channel_mband(df['high'], df['low'], df['close'])
        
        # Donchian Channels
        df['dc_high'] = ta.volatility.donchian_channel_hband(df['high'], df['low'], df['close'])
        df['dc_low'] = ta.volatility.donchian_channel_lband(df['high'], df['low'], df['close'])
        
        return df
        
    def _add_trend_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend indicators."""
        # ADX (Average Directional Index)
        df['adx'] = ta.trend.adx(df['high'], df['low'], df['close'])
        df['adx_pos'] = ta.trend.adx_pos(df['high'], df['low'], df['close'])
        df['adx_neg'] = ta.trend.adx_neg(df['high'], df['low'], df['close'])
        
        # Aroon
        df['aroon_up'] = ta.trend.aroon_up(df['high'], df['low'])
        df['aroon_down'] = ta.trend.aroon_down(df['high'], df['low'])
        
        # CCI (Commodity Channel Index)
        df['cci'] = ta.trend.cci(df['high'], df['low'], df['close'])
        
        # DPO (Detrended Price Oscillator)
        df['dpo'] = ta.trend.dpo(df['close'])
        
        return df
        
    def _add_support_resistance_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add support and resistance indicators."""
        # Pivot Points
        df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
        df['r1'] = 2 * df['pivot'] - df['low']
        df['s1'] = 2 * df['pivot'] - df['high']
        df['r2'] = df['pivot'] + (df['high'] - df['low'])
        df['s2'] = df['pivot'] - (df['high'] - df['low'])
        
        # Fibonacci Retracements (simplified)
        period = 20
        high_period = df['high'].rolling(window=period).max()
        low_period = df['low'].rolling(window=period).min()
        diff = high_period - low_period
        
        df['fib_23.6'] = high_period - 0.236 * diff
        df['fib_38.2'] = high_period - 0.382 * diff
        df['fib_50.0'] = high_period - 0.500 * diff
        df['fib_61.8'] = high_period - 0.618 * diff
        
        return df
        
    def create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create time-based features with minute precision.

        Args:
            df: DataFrame with datetime index

        Returns:
            DataFrame with time features
        """
        logger.info("Creating time features with minute precision")

        result_df = df.copy()

        # Extract time components with minute precision
        result_df['minute'] = result_df.index.minute
        result_df['hour'] = result_df.index.hour
        result_df['day_of_week'] = result_df.index.dayofweek

        # Market session indicators (assuming UTC timezone)
        result_df['is_asian_session'] = ((result_df['hour'] >= 0) & (result_df['hour'] < 8)).astype(int)
        result_df['is_european_session'] = ((result_df['hour'] >= 8) & (result_df['hour'] < 16)).astype(int)
        result_df['is_american_session'] = ((result_df['hour'] >= 16) & (result_df['hour'] < 24)).astype(int)

        # Weekend indicator
        result_df['is_weekend'] = (result_df['day_of_week'] >= 5).astype(int)

        logger.info(f"Created {len(result_df.columns) - len(df.columns)} time features")

        return result_df
        
    def create_lag_features(self, df: pd.DataFrame, columns: List[str], lags: List[int]) -> pd.DataFrame:
        """
        Create lagged features.
        
        Args:
            df: Input DataFrame
            columns: Columns to create lags for
            lags: List of lag periods
            
        Returns:
            DataFrame with lag features
        """
        logger.info(f"Creating lag features for {len(columns)} columns with {len(lags)} lags")
        
        result_df = df.copy()
        
        for col in columns:
            if col in df.columns:
                for lag in lags:
                    result_df[f'{col}_lag_{lag}'] = df[col].shift(lag)
                    
        logger.info(f"Created {len(columns) * len(lags)} lag features")
        
        return result_df
        
    def get_feature_names(self) -> List[str]:
        """
        Get list of feature names.
        
        Returns:
            List of feature names
        """
        return self.feature_names.copy()
        
    def select_features(self, df: pd.DataFrame, feature_list: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Select specific features from DataFrame.
        
        Args:
            df: Input DataFrame
            feature_list: List of features to select (if None, select all)
            
        Returns:
            DataFrame with selected features
        """
        if feature_list is None:
            return df
            
        available_features = [col for col in feature_list if col in df.columns]
        missing_features = [col for col in feature_list if col not in df.columns]
        
        if missing_features:
            logger.warning(f"Missing features: {missing_features}")
            
        return df[available_features]
